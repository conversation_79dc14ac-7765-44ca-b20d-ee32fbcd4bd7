Stack trace:
Frame         Function      Args
0007FFFFBB40  00021006116E (00021028DEE8, 000210272B3E, 0007FFFFBB40, 0007FFFFAA40) msys-2.0.dll+0x2116E
0007FFFFBB40  0002100469BA (000000000000, 000000000000, 00021005B225, 000000000004) msys-2.0.dll+0x69BA
0007FFFFBB40  0002100469F2 (00021028E048, 0007FFFFB9F8, 0007FFFFBB40, 0007FFFFBE20) msys-2.0.dll+0x69F2
0007FFFFBB40  00021006A3FE (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A3FE
0007FFFFBB40  00021006A5ED (0007FFFFBB50, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A5ED
0001004F94B7  00021006B985 (000000004000, 000700000000, 000000000000, 000000000000) msys-2.0.dll+0x2B985
0001004F94B7  00021006BC0A (0007FFFFBEC8, 0007FFFFC0B4, 000000000000, 0000001A3AE0) msys-2.0.dll+0x2BC0A
0001004F94B7  00021019892B (0007FFFFBEC8, 0007FFFFC0B4, 000000000000, 0000001A3AE0) msys-2.0.dll+0x15892B
0001004F94B7  00010042DCCD (000000000000, 000000000000, 000A0017D420, 0007FFFFC0C4) bash.exe+0x2DCCD
0001004EC7B8  00010043C0B6 (0000000000C2, 000A00000000, 000A0004B9F0, 000A000817B0) bash.exe+0x3C0B6
000000000000  00010043E85A (0002102706C0, 000A000603C0, 00021017D8AA, 0007FFFFC0C0) bash.exe+0x3E85A
000000000000  000100441549 (000700000001, 000000000000, 0007FFFFC1B0, 000000000000) bash.exe+0x41549
000000000001  000100442535 (000A00000000, 000A00000000, 000000000000, 000000000000) bash.exe+0x42535
000000000001  0001004445B3 (000100623250, 000000000030, 000A001360F0, 000A000DEAE0) bash.exe+0x445B3
000000000001  0001004155C6 (000A000DEAE0, FFFFFFFFFFFFAE74, 8080808080808080, 0000FFFFFFFF) bash.exe+0x155C6
000A00179120  0001004157DF (0001004EC4A0, 000A000E3C80, 000A000E1C20, 0007FFFFC454) bash.exe+0x157DF
000A00179120  0001004179D6 (0000FFFFFFFF, 000100623E70, 0000FFFFFFFF, 000A00179120) bash.exe+0x179D6
00010061C274  00010041ACA2 (00020000001F, 000A00000000, 00021019892B, 000A00178DA0) bash.exe+0x1ACA2
00010061C274  000100418142 (000000000000, 0002100BEF98, 0007FFFFC600, 000A00047250) bash.exe+0x18142
000A00028A30  00010046F115 (000A0016BCA0, 000000000014, 00010050BCA4, 000A00039E90) bash.exe+0x6F115
000A00028A30  00010046DED7 (000000000009, 000000000000, 0001004ECDE4, 000000000005) bash.exe+0x6DED7
000000000005  00010046E019 (000000000009, 000000000180, 000000000000, 000000000001) bash.exe+0x6E019
000000000005  0001004EAA2B (0007FFFFCBA0, 000A00000160, 0002100480D4, 0007FFFFCBC0) bash.exe+0xEAA2B
0007FFFFCD30  000210048140 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x8140
0007FFFFFFF0  000210045C86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x5C86
0007FFFFFFF0  000210045D34 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x5D34
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE94F50000 ntdll.dll
7FFE93350000 KERNEL32.DLL
7FFE92800000 KERNELBASE.dll
7FFE944E0000 USER32.dll
7FFE92E20000 win32u.dll
7FFE94400000 GDI32.dll
000210040000 msys-2.0.dll
7FFE92D00000 gdi32full.dll
7FFE92C60000 msvcp_win.dll
7FFE92670000 ucrtbase.dll
7FFE92F80000 advapi32.dll
7FFE94E70000 msvcrt.dll
7FFE93CC0000 sechost.dll
7FFE93DD0000 RPCRT4.dll
7FFE92F00000 bcrypt.dll
7FFE91EA0000 CRYPTBASE.DLL
7FFE92770000 bcryptPrimitives.dll
7FFE93F80000 IMM32.DLL
